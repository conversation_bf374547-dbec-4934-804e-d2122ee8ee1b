import { NextRequest, NextResponse } from 'next/server';

/**
 * GET /api/review/[id]
 * Returns review data for a specific review ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: reviewId } = await params;

    if (!reviewId) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Review ID is required' 
        },
        { status: 400 }
      );
    }

    // TODO: Retrieve review data from database/state store
    // For now, we'll simulate review data
    const reviewData = {
      id: reviewId,
      executionId: `exec-${Date.now()}`,
      stepId: 'human-review',
      type: 'content-review',
      status: 'pending',
      createdAt: new Date().toISOString(),
      deadline: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 hours from now
      content: {
        title: 'The Future of AI Automation in Business',
        type: 'blog-article',
        wordCount: 1500,
        artifacts: [
          {
            id: 'final-article',
            type: 'blog-article',
            title: 'The Future of AI Automation in Business',
            content: 'Artificial Intelligence is revolutionizing how businesses operate across industries...',
            metadata: {
              wordCount: 1500,
              readingTime: '6 minutes',
              seoScore: 85
            }
          }
        ]
      },
      instructions: 'Please review the blog article for accuracy, tone, and SEO optimization. Check for grammatical errors and ensure the content aligns with our brand voice.',
      reviewer: 'content-team',
      metadata: {
        priority: 'medium',
        category: 'blog',
        tags: ['ai', 'automation', 'business']
      }
    };

    return NextResponse.json({
      success: true,
      data: reviewData
    });
  } catch (error) {
    console.error('Error getting review data:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to get review data' 
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/review/[id]
 * Submits review decision
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: reviewId } = await params;
    const body = await request.json();
    const { decision, edits, feedback } = body;

    if (!reviewId) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Review ID is required' 
        },
        { status: 400 }
      );
    }

    if (!decision || !['approve', 'reject'].includes(decision)) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Valid decision (approve/reject) is required' 
        },
        { status: 400 }
      );
    }

    // TODO: Process review decision and update workflow
    // For now, we'll simulate the review processing
    
    console.log('Processing review decision:', {
      reviewId,
      decision,
      edits,
      feedback
    });

    const reviewResult = {
      reviewId,
      decision,
      edits: edits || '',
      feedback: feedback || '',
      submittedAt: new Date().toISOString(),
      processed: true
    };

    return NextResponse.json({
      success: true,
      data: reviewResult,
      message: `Review ${decision}d successfully. ${decision === 'approve' ? 'Workflow will continue.' : 'Content will be revised based on feedback.'}`
    });
  } catch (error) {
    console.error('Error processing review decision:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to process review decision' 
      },
      { status: 500 }
    );
  }
}
