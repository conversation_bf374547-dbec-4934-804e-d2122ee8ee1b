import { NextRequest, NextResponse } from 'next/server';
import { SEO_BLOG_POST_TEMPLATE } from '@/core/workflow/templates';

/**
 * GET /api/workflow/execution/[id]
 * Returns execution status and details
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: executionId } = await params;

    if (!executionId) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Execution ID is required' 
        },
        { status: 400 }
      );
    }

    // TODO: Retrieve execution from database/state store
    // For now, we'll simulate execution status using the actual template structure
    const template = SEO_BLOG_POST_TEMPLATE;
    const templateSteps = template.workflow.steps;

    // Simulate current execution state - currently at content-creation step
    const currentStepIndex = 2; // content-creation is the 3rd step (index 2)

    const steps = templateSteps.map((step, index) => {
      if (index < currentStepIndex) {
        return {
          id: step.id,
          name: step.name,
          status: 'completed',
          completedAt: new Date(Date.now() - (templateSteps.length - index) * 60 * 1000).toISOString()
        };
      } else if (index === currentStepIndex) {
        return {
          id: step.id,
          name: step.name,
          status: 'running',
          startedAt: new Date().toISOString()
        };
      } else {
        return {
          id: step.id,
          name: step.name,
          status: 'pending'
        };
      }
    });

    const execution = {
      id: executionId,
      templateId: 'blog-post-seo',
      status: 'running',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      currentStep: templateSteps[currentStepIndex].id,
      progress: Math.round((currentStepIndex / templateSteps.length) * 100),
      steps,
      artifacts: {
        'keyword-research': {
          id: 'keyword-research-artifact',
          type: 'keyword-analysis',
          title: 'Keyword Research Results',
          content: {
            primary_keywords: ['AI automation', 'workflow optimization', 'business automation'],
            long_tail_keywords: ['AI automation tools for small business', 'workflow optimization strategies'],
            semantic_keywords: ['artificial intelligence', 'process automation', 'digital transformation'],
            content_structure: ['Introduction to AI automation', 'Benefits for businesses', 'Implementation strategies'],
            competitor_insights: ['Top competitors focus on enterprise solutions', 'Gap in SMB market']
          },
          status: 'completed'
        }
      },
      metadata: {
        estimatedCompletion: new Date(Date.now() + 30 * 60 * 1000).toISOString(),
        agentsInvolved: ['seo-keyword', 'market-research', 'content-strategy'],
        template: template.name,
        consultationEnabled: template.consultationEnabled || false
      }
    };

    return NextResponse.json({
      success: true,
      data: execution
    });
  } catch (error) {
    console.error('Error getting execution status:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to get execution status' 
      },
      { status: 500 }
    );
  }
}
