import { NextRequest, NextResponse } from 'next/server';
import { DynamicWorkflowOrchestrator } from '../../dynamic-workflow-orchestrator';
import { stateStore } from '../../../../../api/agents/collaborative-iteration/utils/stateStore';
import logger from '../../../../../api/agents/collaborative-iteration/utils/logger';

/**
 * API handler for getting a specific dynamic collaboration session
 *
 * @param req The Next.js request object
 * @param params The route parameters containing the session ID
 * @returns NextResponse with the session data
 */
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ sessionId: string }> }
): Promise<NextResponse> {
  try {
    const { sessionId } = await params;

    // Validate session ID
    if (!sessionId) {
      return NextResponse.json(
        { error: 'Missing required parameter: sessionId' },
        { status: 400 }
      );
    }

    // Get the session state from the state store
    const sessionState = await stateStore.getState(sessionId);

    if (!sessionState) {
      return NextResponse.json(
        { error: 'Session not found' },
        { status: 404 }
      );
    }

    logger.info(`Retrieved dynamic collaboration session`, {
      sessionId
    });

    return NextResponse.json(sessionState);
  } catch (error) {
    const err = error as Error;
    logger.error(`Error retrieving dynamic collaboration session`, {
      sessionId: params.sessionId,
      error: err.message || String(error),
      stack: err.stack
    });

    return NextResponse.json(
      { error: err.message || 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * API handler for updating a specific dynamic collaboration session
 *
 * @param req The Next.js request object
 * @param params The route parameters containing the session ID
 * @returns NextResponse with the updated session data
 */
export async function PUT(
  req: NextRequest,
  { params }: { params: Promise<{ sessionId: string }> }
): Promise<NextResponse> {
  try {
    const { sessionId } = await params;

    // Validate session ID
    if (!sessionId) {
      return NextResponse.json(
        { error: 'Missing required parameter: sessionId' },
        { status: 400 }
      );
    }

    // Parse the request body
    const body = await req.json();
    const { action } = body;

    // Create a new orchestrator instance
    const orchestrator = new DynamicWorkflowOrchestrator(sessionId);

    // Perform the requested action
    switch (action) {
      case 'pause':
        await orchestrator.pauseCollaboration();
        break;
      case 'resume':
        await orchestrator.resumeCollaboration();
        break;
      case 'cancel':
        await orchestrator.cancelCollaboration();
        break;
      default:
        return NextResponse.json(
          { error: `Unknown action: ${action}` },
          { status: 400 }
        );
    }

    // Get the updated session state
    const updatedState = await stateStore.getState(sessionId);

    logger.info(`Updated dynamic collaboration session`, {
      sessionId,
      action
    });

    return NextResponse.json({
      success: true,
      message: `Session ${action} successful`,
      state: updatedState
    });
  } catch (error) {
    const err = error as Error;
    logger.error(`Error updating dynamic collaboration session`, {
      sessionId: params.sessionId,
      error: err.message || String(error),
      stack: err.stack
    });

    return NextResponse.json(
      { error: err.message || 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * API handler for deleting a specific dynamic collaboration session
 *
 * @param req The Next.js request object
 * @param params The route parameters containing the session ID
 * @returns NextResponse with the result of the deletion
 */
export async function DELETE(
  req: NextRequest,
  { params }: { params: { sessionId: string } }
): Promise<NextResponse> {
  try {
    const { sessionId } = params;

    // Validate session ID
    if (!sessionId) {
      return NextResponse.json(
        { error: 'Missing required parameter: sessionId' },
        { status: 400 }
      );
    }

    // Delete the session state from the state store
    // Note: In a real implementation, you would also clean up any related resources
    await stateStore.deleteState(sessionId);

    logger.info(`Deleted dynamic collaboration session`, {
      sessionId
    });

    return NextResponse.json({
      success: true,
      message: 'Session deleted successfully'
    });
  } catch (error) {
    const err = error as Error;
    logger.error(`Error deleting dynamic collaboration session`, {
      sessionId: params.sessionId,
      error: err.message || String(error),
      stack: err.stack
    });

    return NextResponse.json(
      { error: err.message || 'An unknown error occurred' },
      { status: 500 }
    );
  }
}
